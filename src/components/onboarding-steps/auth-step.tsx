import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AuthStepData, OnboardingStepProps } from '@/types/onboarding';
import { useState } from 'react';
import { StepCard } from '../common';
import { StepNavigation } from '../common/step-navigation';

interface AuthStepProps extends OnboardingStepProps<AuthStepData> {
  initialValues?: AuthStepData;
  onBack?: () => void;
}

export default function AuthStep({
  onNext,
  onBack,
  onUpdate,
  initialValues,
}: AuthStepProps) {
  const [formData, setFormData] = useState<AuthStepData>({
    email: initialValues?.email || '',
    password: initialValues?.password || '',
    fullName: initialValues?.fullName || '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onUpdate(formData);
      onNext();
    }
  };

  const handleInputChange = (field: keyof AuthStepData, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    onUpdate(updatedData);

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <StepCard
      title="Create Your Account"
      description="Let's get you started with SEO45. You'll only pay after setting up your website."
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name (Optional)</Label>
          <Input
            id="fullName"
            type="text"
            placeholder="Enter your full name"
            value={formData.fullName}
            onChange={e => handleInputChange('fullName', e.target.value)}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={formData.email}
            onChange={e => handleInputChange('email', e.target.value)}
            className={errors.email ? 'border-destructive' : ''}
          />
          {errors.email && (
            <p className="text-sm text-destructive">{errors.email}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Create a secure password"
            value={formData.password}
            onChange={e => handleInputChange('password', e.target.value)}
            className={errors.password ? 'border-destructive' : ''}
          />
          {errors.password && (
            <p className="text-sm text-destructive">{errors.password}</p>
          )}
          <p className="text-xs text-muted-foreground">
            Password must be at least 6 characters long
          </p>
        </div>
      </div>
      <div className="mt-4 text-center">
        <p className="text-sm text-muted-foreground">
          Already have an account?{' '}
          <a href="/auth/login" className="text-primary hover:underline">
            Sign in here
          </a>
        </p>
      </div>
      <StepNavigation onNext={handleNext} onBack={onBack} />
    </StepCard>
  );
}
