import { Card, CardContent } from '@/components/ui/card';
import { AuthHeader } from './auth-header';

interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export function AuthLayout({
  children,
  title = 'Welcome Back',
  description = 'Sign in to your account to continue',
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6">
        <AuthHeader title={title} description={description} />
        <Card className="border-border shadow-lg">
          <CardContent className="p-6">{children}</CardContent>
        </Card>
      </div>
    </div>
  );
}
