import { AuthLayout } from '@/components/auth';
import { FormPasswordField, FormTextField } from '@/components/forms';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/stores';
import { Loader2, UserPlus } from 'lucide-react';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export default function LoginPage() {
  const { signIn } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const form = useForm({ defaultValues: { email: '', password: '' } });

  const handleSignIn = async (data: { email: string; password: string }) => {
    setLoading(true);
    const { error } = await signIn(data.email, data.password);
    if (error) {
      toast.error('Sign in failed: ' + error.message);
    } else {
      toast.success("You've been signed in successfully.");
      // The AuthRouteGuard will handle redirecting to the appropriate dashboard
    }
    setLoading(false);
  };

  const handleCreateAccount = () => {
    // Navigate to signup page
    navigate('/auth/signup');
  };

  return (
    <AuthLayout
      title="Welcome Back"
      description="Sign in to your account to continue"
    >
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(handleSignIn)} className="space-y-4">
          <FormTextField
            name="email"
            label="Email"
            placeholder="Enter your email"
            required
          />
          <FormPasswordField
            name="password"
            label="Password"
            placeholder="Enter your password"
            required
          />
          <Button type="submit" className="w-full h-11" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sign In
          </Button>
          <Button
            type="button"
            variant="link"
            className="w-full text-sm text-muted-foreground hover:text-primary"
            asChild
          >
            <Link to="/auth/forgot-password">Forgot your password?</Link>
          </Button>
        </form>
      </FormProvider>

      {/* Divider */}
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-border" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-card px-2 text-muted-foreground">or</span>
        </div>
      </div>

      {/* Create Account Button */}
      <Button
        type="button"
        variant="outline"
        className="w-full h-11"
        onClick={handleCreateAccount}
      >
        <UserPlus className="mr-2 h-4 w-4" />
        Create New Account
      </Button>
      <p className="text-xs text-muted-foreground text-center mt-4">
        New to SEO45? Creating an account will guide you through our onboarding
        process.
      </p>
    </AuthLayout>
  );
}
