import { AuthLayout } from '@/components/auth';
import { FormTextField } from '@/components/forms';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/stores';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';

interface ForgotPasswordFormData {
  email: string;
}

export default function ForgotPasswordPage() {
  const { resetPassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const form = useForm<ForgotPasswordFormData>({
    defaultValues: {
      email: '',
    },
  });

  const handleResetPassword = async (data: ForgotPasswordFormData) => {
    setLoading(true);
    const { error } = await resetPassword(data.email);
    if (error) {
      toast.error('Reset password failed: ' + error.message);
    } else {
      setSuccess(true);
      toast.success('Check your email for a password reset link.');
    }
    setLoading(false);
  };

  if (success) {
    return (
      <AuthLayout
        title="Check Your Email"
        description="We've sent a password reset link to your email address"
      >
        <div className="text-center space-y-4">
          <div className="text-sm text-muted-foreground">
            If you don't see the email in your inbox, please check your spam
            folder.
          </div>
          <Button variant="outline" className="w-full h-11" asChild>
            <Link to="/auth/login">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sign In
            </Link>
          </Button>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Reset Password"
      description="Enter your email address and we'll send you a reset link"
    >
      <div className="space-y-4">
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          asChild
        >
          <Link to="/auth/login">
            <ArrowLeft className="h-4 w-4" />
            Back to Sign In
          </Link>
        </Button>

        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(handleResetPassword)}
            className="space-y-4"
          >
            <FormTextField
              name="email"
              label="Email"
              placeholder="Enter your email"
              type="email"
              required
            />
            <Button type="submit" className="w-full h-11" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Send Reset Link
            </Button>
          </form>
        </FormProvider>
      </div>
    </AuthLayout>
  );
}
