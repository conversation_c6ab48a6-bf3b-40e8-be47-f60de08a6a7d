import { AuthLayout } from '@/components/auth';
import { FormPasswordField, FormTextField } from '@/components/forms';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/stores';
import { Loader2, LogIn } from 'lucide-react';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface SignupFormData {
  fullName: string;
  email: string;
  password: string;
}

export default function SignupPage() {
  const { signUp } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const form = useForm<SignupFormData>({
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
    },
  });

  const handleSignUp = async (data: SignupFormData) => {
    setLoading(true);
    const { error } = await signUp(data.email, data.password, data.fullName);
    if (error) {
      toast.error('Sign up failed: ' + error.message);
    } else {
      toast.success(
        'Account created successfully! Please check your email to verify your account.'
      );
      // Redirect to onboarding after successful signup
      navigate('/onboarding?step=website');
    }
    setLoading(false);
  };

  const handleSignIn = () => {
    // Navigate to login page
    navigate('/auth/login');
  };

  return (
    <AuthLayout
      title="Create Account"
      description="Join SEO45 and start creating amazing content"
    >
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(handleSignUp)} className="space-y-4">
          <FormTextField
            name="fullName"
            label="Full Name"
            placeholder="Enter your full name"
            required
          />
          <FormTextField
            name="email"
            label="Email"
            placeholder="Enter your email"
            type="email"
            required
          />
          <FormPasswordField
            name="password"
            label="Password"
            placeholder="Create a password (min. 6 characters)"
            required
          />
          <Button type="submit" className="w-full h-11" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Account
          </Button>
        </form>
      </FormProvider>

      <p className="text-xs text-muted-foreground text-center mt-4">
        By creating an account, you agree to our Terms of Service and Privacy
        Policy
      </p>

      {/* Divider */}
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-border" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-card px-2 text-muted-foreground">or</span>
        </div>
      </div>

      {/* Sign In Button */}
      <Button
        type="button"
        variant="outline"
        className="w-full h-11"
        onClick={handleSignIn}
      >
        <LogIn className="mr-2 h-4 w-4" />
        Sign In to Existing Account
      </Button>
      <p className="text-xs text-muted-foreground text-center mt-4">
        Already have an account? Sign in to access your dashboard.
      </p>
    </AuthLayout>
  );
}
