// Navigation utilities
export const navigateTo = (path: string) => {
  window.history.pushState({}, '', path);
  window.dispatchEvent(new PopStateEvent('popstate'));
};

// Route path constants
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  ONBOARDING: '/onboarding',
  AUTH_LOGIN: '/auth/login',
  AUTH_SIGNUP: '/auth/signup',
  AUTH_FORGOT_PASSWORD: '/auth/forgot-password',
} as const;

// Helper to check if current path matches a route
export const isCurrentPath = (path: string): boolean => {
  return window.location.pathname === path;
};

// Helper to check if current path starts with a base path
export const isCurrentPathBase = (basePath: string): boolean => {
  return window.location.pathname.startsWith(basePath);
};
