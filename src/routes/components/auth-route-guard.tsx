import MainLoading from '@/components/loaders/main-loading';
import { useAuth } from '@/stores';
import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

interface AuthRouteGuardProps {
  children: React.ReactNode;
}

export default function AuthRouteGuard({ children }: AuthRouteGuardProps) {
  const { user, profile, loading, isAuthenticated, getRedirectPath } =
    useAuth();
  const location = useLocation();

  useEffect(() => {
    // This effect runs when auth state changes
    // The navigation logic is handled in the render
  }, [user, profile, loading]);

  // Show loading while initializing auth
  if (loading) {
    return <MainLoading />;
  }

  // Define auth routes
  const authRoutes = ['/auth/login', '/auth/signup', '/auth/forgot-password'];
  const isOnAuthRoute = authRoutes.includes(location.pathname);

  // If not authenticated and not on auth route, redirect to login
  if (!isAuthenticated && !isOnAuthRoute) {
    return <Navigate to="/auth/login" replace />;
  }

  // If authenticated and on auth route, redirect to appropriate dashboard
  if (isAuthenticated && isOnAuthRoute) {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  // If authenticated and on root path, redirect to appropriate dashboard
  if (isAuthenticated && location.pathname === '/') {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
}
