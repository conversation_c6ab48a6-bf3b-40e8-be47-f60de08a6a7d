import { lazy } from 'react';
// import ArticlesPage from '../pages/dashboard/articles';
import { adminRoutes } from './admin/config';
import AuthRedirect from './components/auth-redirect';
import DashboardGuard from './components/dashboard-guard';
import { RouteConfig } from './types';

// Lazy load pages
const LoginPage = lazy(() => import('@/pages/auth/login-page'));
const SignupPage = lazy(() => import('@/pages/auth/signup-page'));
const ForgotPasswordPage = lazy(
  () => import('@/pages/auth/forgot-password-page')
);
const OnboardingPage = lazy(() => import('@/pages/onboarding/onboarding-page'));
const NotFoundPage = lazy(() => import('@/pages/not-found'));

// Dashboard pages
const OverviewPage = lazy(
  () => import('@/pages/dashboard/overview/overview-page')
);
const WebsitesPage = lazy(
  () => import('@/pages/dashboard/websites/websites-page')
);
const ArticlesPage = lazy(() => import('@/pages/dashboard/articles'));

const SubscriptionsPage = lazy(
  () => import('@/pages/dashboard/subscription/subscriptions-page')
);

const ProfilePage = lazy(() => import('@/pages/dashboard/profile'));

// Route configurations
export const routes: RouteConfig[] = [
  {
    path: '/',
    component: DashboardGuard,
    protected: true,
    index: true,
    layout: 'none',
  },
  {
    path: '/dashboard',
    component: OverviewPage,
    protected: true,
    layout: 'dashboard',
  },

  {
    path: '/dashboard/websites',
    component: WebsitesPage,
    protected: true,
    layout: 'dashboard',
  },
  {
    path: '/dashboard/articles',
    component: ArticlesPage,
    protected: true,
    layout: 'dashboard',
  },

  {
    path: '/dashboard/subscriptions',
    component: SubscriptionsPage,
    protected: true,
    layout: 'dashboard',
  },
  {
    path: '/dashboard/profile',
    component: ProfilePage,
    protected: true,
    layout: 'dashboard',
  },
  {
    path: '/onboarding',
    component: OnboardingPage,
    protected: true,
    layout: 'root',
  },
  {
    path: '/auth/login',
    component: LoginPage,
    protected: false,
    layout: 'root',
  },
  {
    path: '/auth/signup',
    component: SignupPage,
    protected: false,
    layout: 'root',
  },
  {
    path: '/auth/forgot-password',
    component: ForgotPasswordPage,
    protected: false,
    layout: 'root',
  },
  // Redirect old auth route to login for backward compatibility
  {
    path: '/auth',
    component: AuthRedirect,
    protected: false,
    layout: 'none',
  },
  ...adminRoutes,
];

export { NotFoundPage };
